const admin = require("firebase-admin");
const db = admin.firestore();

const {
  fetchClosingItems,
  createClosing,
  fetchClosingData,
  getById,
  prepareClosingItems,
} = require("@/repositories/closingRepo");
const { getNextClosingId } = require("./counterService");
const schema = require("@/schema/closingSchema");
const { handleValidation } = require("@/utils/validation");
const {
  FirestoreDateHelper: FD,
  TIME_OPTION,
} = require("@/helpers/dateHelper");
const {
  getInventoryItemStocks,
  getBusinessDate,
  getStockMovementItems,
} = require("@/repositories/stockRepo");
const { LedgerTypes, StockLedgerReasonCode } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("./stockService");
const {
  applyLedgersToDailyStock,
  applyPhysicalClosing,
  recalculateStockFromDate,
} = require("./stockMovementsService");

const getClosingItemsRequest = async (tenantId, locationId) => {
  const closingItems = await fetchClosingItems(tenantId, locationId);
  return closingItems;
};

const getClosingById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      closingDate: FD.toFormattedDate(data.closingDate),
      closedBy: {
        ...data.closedBy,
        time: FD.toFormattedDate(data.closedBy.time),
      },
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const getClosingDataRequest = async (filters) => {
  const closingData = await fetchClosingData(filters);

  const result = closingData.map((doc) => ({
    closingNumber: doc.closingNumber,
    id: doc.id,
    closingDate: FD.toFormattedDate(doc.closingDate),
    locationName: doc.locationName,
    createdDate: FD.toFormattedDate(doc.closedBy.time),
    workAreaName: doc.workAreaName,
    closedBy: doc.closedBy.userName,
    stockCorrection: doc.stockCorrection,
  }));

  return result;
};

const createClosingRequest = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const closingNumber = await getNextClosingId(validatedData.tenantId);

    const ledgers = [];
    const today = FD.now(TIME_OPTION.START);
    const businessDateToday = getBusinessDate(today);
    const eventDate = FD.toFirestore(
      validatedData.closingDate,
      TIME_OPTION.START
    );
    const businessDateEventDate = getBusinessDate(eventDate);

    const createdClosing = await db.runTransaction(async (trans) => {
      validatedData.items = await prepareClosingItems(
        validatedData.tenantId,
        validatedData.workAreaId,
        validatedData.items
      );
      if (validatedData.stockCorrection) {
        const items =
          businessDateEventDate === businessDateToday
            ? await getInventoryItemStocks(
                validatedData.workAreaId,
                validatedData.items
              )
            : await getStockMovementItems(
                validatedData.tenantId,
                validatedData.locationId,
                validatedData.workAreaId,
                businessDateEventDate,
                validatedData.items
              );

        // CREDIT ITEMS
        for (const item of items.filter(
          (i) => i.closingQtyInRecipeUOM > i.recipeQtyInStock
        )) {
          const qty = item.convertedClosingQuantity - item.inStock;
          const qtyInRecipeUOM =
            item.closingQtyInRecipeUOM - item.recipeQtyInStock;
          const uom =
            item.pkg && item.pkg.id !== "default"
              ? item.pkg.name
              : item.purchaseUOM; // purchaseUOM required
          const ledger = await creditStock(
            {
              ledgerType: LedgerTypes.ADJUSTMENT,
              reasonCode: StockLedgerReasonCode.PHYSICAL_STOCK_OVERRIDE,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              qtyInRecipeUOM,
              pkgUOM: uom,
              recipeUOM: item.recipeUOM || "",
              conversionFactor: item.conversionFactor || 1,
              unitCost: item.unitCost,
              totalCost: item.unitCost * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
              eventDate: validatedData.closingDate,
            },
            trans
          );
          ledgers.push({
            ...ledger,
            qty: item.convertedClosingQuantity,
            recipeQty: item.closingQtyInRecipeUOM,
            totalCost: item.convertedClosingQuantity * item.unitCost,
          });
        }

        // DEBIT ITEMS
        for (const item of items.filter(
          (i) => i.closingQtyInRecipeUOM < i.recipeQtyInStock
        )) {
          const uom =
            item.pkg && item.pkg.id !== "default"
              ? item.pkg.name
              : item.purchaseUOM; // purchaseUOM required
          const qty = item.inStock - item.convertedClosingQuantity;
          const qtyInRecipeUOM =
            item.recipeQtyInStock - item.closingQtyInRecipeUOM;

          const ledger = await debitStock(
            {
              ledgerType: LedgerTypes.ADJUSTMENT,
              reasonCode: StockLedgerReasonCode.PHYSICAL_STOCK_OVERRIDE,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              qtyInRecipeUOM,
              pkgUOM: uom,
              unitCost: item.unitCost,
              totalCost: item.unitCost * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
              eventDate: validatedData.closingDate,
            },
            trans
          );

          ledgers.push({
            ...ledger,
            qty: item.convertedClosingQuantity,
            recipeQty: item.closingQtyInRecipeUOM,
            totalCost: item.convertedClosingQuantity * item.unitCost,
          });
        }
      }

      const requestData = {
        ...validatedData,
        closingNumber,
      };

      const closingDoc = await createClosing(requestData, trans);
      return closingDoc;
    });

    const infoData = {
      tenantId: validatedData.tenantId,
      locationId: validatedData.locationId,
      locationName: validatedData.locationName,
      inventoryLocationId: validatedData.workAreaId,
      inventoryLocationName: validatedData.workAreaName,
      eventDate,
    };

    let affectedItems;

    affectedItems = ledgers.map((l) => ({
      itemId: l.itemId,
      itemCode: l.itemCode,
      itemName: l.itemName,
      qty: l.qty,
      qtyInRecipeUOM: l.recipeQty,
      uom: l.pkgUOM,
      unitCost: l.unitCost,
      totalCost: l.totalCost,
      categoryName: l.categoryName,
      categoryId: l.categoryId,
      subcategoryId: l.subcategoryId,
      subcategoryName: l.subcategoryName,
      pkg: {
        id: l.pkg?.id,
        name: l.pkg?.name,
      },
    }));

    if (!validatedData.stockCorrection) {
      affectedItems = validatedData.items.map((i) => ({
        itemId: i.itemId,
        itemCode: i.itemCode,
        itemName: i.itemName,
        qty: i.closingQuantity,
        qtyInRecipeUOM: i.closingQtyInRecipeUOM,
        uom: i.pkg?.name,
        unitCost: i.unitCost,
        totalCost: i.unitCost * i.closingQuantity,
        pkg: {
          id: i.pkg?.id,
          name: i.pkg?.name,
        },
        categoryId: i.categoryId,
        subcategoryId: i.subcategoryId,
        categoryName: i.categoryName,
        subcategoryName: i.subcategoryName,
      }));
    }

    await applyPhysicalClosing(
      {
        ...infoData,
        items: affectedItems,
      },
      validatedData.stockCorrection
    );

    await recalculateStockFromDate({
      ...infoData,
      startDate: eventDate,
      items: affectedItems.map((i) => `${i.itemId}_${i.pkg.id}`),
    });

    return createdClosing;
  } catch (error) {
    console.error("Error in createClosingRequest:", error);
    throw new Error(error.message);
  }
};

module.exports = {
  getClosingItemsRequest,
  createClosingRequest,
  getClosingDataRequest,
  getClosingById,
};
