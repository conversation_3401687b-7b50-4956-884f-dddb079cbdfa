// repositories/consumptionRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const CONSUMPTION_COLLECTION = db.collection(COLLECTIONS.CONSUMPTION_TRACKING);

async function createConsumption(data) {
  const ref = db.collection(CONSUMPTION_COLLECTION).doc();
  await ref.set({ ...data, id: ref.id });
  return { id: ref.id, ...data };
}

module.exports = {
  createConsumption,
};
