// schemas/consumptionDataSchema.js
const Joi = require("joi");

const invItemSchema = Joi.object({
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  itemType: Joi.string().valid('bought', 'produced', 'semi-finished').required(),
  recipeUom: Joi.string().required(),
  cost: Joi.number().min(0).required(),
  requiredQty: Joi.number().positive().required(),
});

const consumptionDataSchema = Joi.object({
  saleReferenceId: Joi.number().integer().positive().required(),
  accountId: Joi.number().integer().positive().required(),
  storeId: Joi.number().integer().positive().required(),
  ticketNo: Joi.string().required(),
  tableNo: Joi.string().required(),
  floorNo: Joi.number().integer().positive().required(),
  
  // POS Menu Item details
  posMenuItemName: Joi.string().required(),
  posItemCode: Joi.string().required(),
  
  // Serving size details
  servingSizeId: Joi.number().integer().positive().required(),
  servingSizeName: Joi.string().required(),
  
  // Sale details
  soldQuantity: Joi.number().positive().required(),
  posItemTotalAmount: Joi.number().min(0).required(),
  
  // Work area and location (optional for error cases)
  workArea: Joi.string().allow(null).optional(),
  workAreaId: Joi.string().allow(null).optional(),
  location: Joi.string().allow(null).optional(),
  locationId: Joi.string().allow(null).optional(),
  
  // Inventory item details (optional, present only when linked)
  invItemName: Joi.string().optional(),
  invItemCode: Joi.string().optional(),
  invItems: Joi.array().items(invItemSchema).optional(),
  
  // Status fields (for error tracking)
  status: Joi.string().valid('success', 'error', 'warning').optional(),
  statusSummary: Joi.string().optional(),
});

module.exports = consumptionDataSchema;